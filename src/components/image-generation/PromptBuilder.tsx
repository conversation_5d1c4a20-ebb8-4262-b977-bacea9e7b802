import React, { useState, useCallback, useEffect } from 'react';
import { History, Star, Info, X, ChevronDown } from 'lucide-react';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Badge } from '../ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { cn } from '../common/utils/utils';
import { BLOCK_COLORS } from '../../config/imageGeneration.config';

export interface ActiveBlock {
  type: string;
  name: string;
  text: string;
}

interface PromptBuilderProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
  activeBlocks: ActiveBlock[];
  onActiveBlocksChange: (blocks: ActiveBlock[]) => void;
  onShowHistory?: () => void;
  className?: string;
}

export function PromptBuilder({
  prompt,
  onPromptChange,
  activeBlocks,
  onActiveBlocksChange,
  onShowHistory,
  className
}: PromptBuilderProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [localPrompt, setLocalPrompt] = useState(prompt);

  useEffect(() => {
    setLocalPrompt(prompt);
  }, [prompt]);

  const removeBlock = useCallback((blockText: string) => {
    const newBlocks = activeBlocks.filter(block => block.text !== blockText);
    onActiveBlocksChange(newBlocks);
  }, [activeBlocks, onActiveBlocksChange]);

  const parsePromptSegments = useCallback((promptText: string) => {
    const segments: Array<{ text: string; type: string | null; blockData?: ActiveBlock }> = [];
    let remainingPrompt = promptText;
    
    // Sort blocks by position in prompt
    const blocksWithPositions = activeBlocks
      .map(block => ({
        ...block,
        index: promptText.indexOf(block.text)
      }))
      .filter(block => block.index !== -1)
      .sort((a, b) => a.index - b.index);
    
    let lastIndex = 0;
    blocksWithPositions.forEach(block => {
      const index = block.index;
      
      // Add text before block
      if (index > lastIndex) {
        segments.push({ text: promptText.substring(lastIndex, index), type: null });
      }
      
      // Add block
      segments.push({ 
        text: block.text, 
        type: block.type,
        blockData: block
      });
      
      lastIndex = index + block.text.length;
    });
    
    // Add remaining text
    if (lastIndex < promptText.length) {
      segments.push({ text: promptText.substring(lastIndex), type: null });
    }
    
    return segments;
  }, [activeBlocks]);

  const handlePromptChange = useCallback((value: string) => {
    setLocalPrompt(value);
    onPromptChange(value);
  }, [onPromptChange]);

  const clearBlocks = useCallback(() => {
    onActiveBlocksChange([]);
    onPromptChange('');
  }, [onActiveBlocksChange, onPromptChange]);

  const getBlockStyle = useCallback((type: string) => {
    const colorMap: Record<string, string> = {
      model: 'rgba(59, 130, 246, 0.2)',
      angle: 'rgba(16, 185, 129, 0.2)',
      garment: 'rgba(139, 92, 246, 0.2)',
      background: 'rgba(251, 146, 60, 0.2)',
      artDirection: 'rgba(236, 72, 153, 0.2)',
    };
    return { backgroundColor: colorMap[type] || 'transparent' };
  }, []);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <Label className="text-sm font-medium">Prompt Builder</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                  <Info className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-1 text-xs">
                  <p className="font-medium mb-1">Prompt Structure:</p>
                  <p>1. <span className="font-medium">Place/Create</span> + <span className="font-medium">Model</span> description</p>
                  <p>2. <span className="font-medium">wearing this</span> + Main garment</p>
                  <p>3. <span className="font-medium">with these</span> + Additional garments</p>
                  <p>4. <span className="font-medium">on this</span> + Background/Setting</p>
                  <p>5. <span className="font-medium">Angle</span> + Camera position</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="flex items-center gap-1">
          {onShowHistory && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 text-xs"
              onClick={onShowHistory}
            >
              <History className="w-3 h-3 mr-1" />
              History
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 text-xs"
            onClick={clearBlocks}
          >
            Clear Blocks
          </Button>
        </div>
      </div>

      {/* Active Blocks */}
      {activeBlocks.length > 0 && (
        <div>
          <Label className="text-xs text-muted-foreground mb-2 block">Active Blocks</Label>
          <div className="flex flex-wrap gap-1.5">
            {activeBlocks.map((block, index) => {
              const colors = BLOCK_COLORS[block.type as keyof typeof BLOCK_COLORS] || BLOCK_COLORS.model;
              return (
                <Badge
                  key={index}
                  variant="secondary"
                  className={cn(
                    "text-xs px-2 py-1 cursor-pointer border transition-all",
                    colors.bg,
                    colors.text,
                    colors.border,
                    "hover:opacity-80"
                  )}
                >
                  {block.name}
                  <X
                    className="w-3 h-3 ml-1 opacity-60 hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeBlock(block.text);
                    }}
                  />
                </Badge>
              );
            })}
          </div>
        </div>
      )}

      {/* Prompt Editor */}
      <div className="relative">
        <div className="relative group">
          <div 
            className={cn(
              "min-h-[150px] max-h-[300px] overflow-y-auto text-base p-4 border-2 rounded-lg bg-white cursor-text leading-relaxed transition-all hover:border-primary/50",
              isEditing && "border-primary ring-2 ring-primary/20"
            )}
            onClick={() => {
              const textarea = document.getElementById('prompt-textarea');
              if (textarea) textarea.focus();
            }}
          >
            {localPrompt ? (
              <div className="whitespace-pre-wrap break-words">
                {parsePromptSegments(localPrompt).map((segment, index) => (
                  segment.type ? (
                    <span
                      key={index}
                      className="relative px-0.5 rounded-sm"
                      style={getBlockStyle(segment.type)}
                    >
                      {segment.text}
                    </span>
                  ) : (
                    <span key={index}>{segment.text}</span>
                  )
                ))}
              </div>
            ) : (
              <span className="text-muted-foreground text-base">
                Build your prompt by selecting blocks or type directly...
              </span>
            )}
          </div>
          
          {/* Overlay textarea for editing */}
          <Textarea
            id="prompt-textarea"
            className="absolute inset-0 resize-none bg-transparent p-4 text-base leading-relaxed focus:outline-none cursor-text"
            style={{ 
              caretColor: 'rgb(37, 99, 235)',
              color: 'rgba(0, 0, 0, 0.05)',
              WebkitTextFillColor: 'rgba(0, 0, 0, 0.05)',
            }}
            value={localPrompt}
            onChange={(e) => handlePromptChange(e.target.value)}
            onFocus={() => setIsEditing(true)}
            onBlur={() => setIsEditing(false)}
            placeholder=""
            spellCheck={false}
          />
          
          <div className="absolute top-2 right-2 bg-white/90 backdrop-blur rounded px-2 py-1 pointer-events-none">
            <div className="text-xs text-muted-foreground">
              {isEditing ? (
                <span className="text-primary font-medium flex items-center gap-1">
                  <span className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse" />
                  Editing
                </span>
              ) : (
                <span>
                  <span className="font-medium text-primary">{activeBlocks.length}</span> blocks
                </span>
              )}
            </div>
          </div>
        </div>
        
        {/* Info and legend */}
        <div className="mt-2 space-y-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>
              {isEditing ? 'Type to edit prompt • Blue cursor shows position' : 'Click prompt to edit • Highlighted text = AI blocks'}
            </span>
            <span>{localPrompt.length} chars</span>
          </div>
          <div className="flex items-center gap-2 flex-wrap text-xs">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(59, 130, 246, 0.2)' }} />
              <span>Model</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(16, 185, 129, 0.2)' }} />
              <span>Angle</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(139, 92, 246, 0.2)' }} />
              <span>Garment</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: 'rgba(251, 146, 60, 0.2)' }} />
              <span>Background</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}