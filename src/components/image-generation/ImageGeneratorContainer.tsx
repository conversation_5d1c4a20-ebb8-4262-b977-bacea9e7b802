import React, { useState, useReducer, useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Card } from '../ui/card';
import { ArrowLeft, Sparkles } from 'lucide-react';
import { toast } from 'react-hot-toast';

import { useCollection } from '../common/hooks/useCollections';
import { useOrganization } from '../common/hooks/useOrganizations';

import { V2ImageUploader, V2Images } from './V2ImageUploader';
import { PromptBuilder, ActiveBlock } from './PromptBuilder';
import { GeneratedImageGrid, GeneratedImage } from './GeneratedImageGrid';
import { ImageSelectionPanel } from './ImageSelectionPanel';
import { TechnicalSettings } from './TechnicalSettings';
import { ImageDetailModal } from './ImageDetailModal';
import { FlexibleInputBox, FlexibleInput } from '../image-generator/FlexibleInputBox';
import { BackgroundVisualDirection, BackgroundImage } from './BackgroundVisualDirection';

import { UnifiedImageGenerationService, UnifiedGenerationParams } from '../../services/unifiedImageGeneration';
import { useFashionLabImages } from '../../hooks/useFashionLabImages';
import { getOrCreateDemoCollection } from '../../utils/demoHelpers';
import { 
  CAMPAIGN_MODELS, 
  ANGLE_BLOCKS, 
  DEFAULT_GENERATION_SETTINGS,
  Model,
  AngleBlock
} from '../../config/imageGeneration.config';

// State management
interface GeneratorState {
  // Selection state
  selectedModels: string[];
  selectedAngles: number[];
  selectedGarments: string[];
  selectedSettings: Record<string, number>;

  // V2 Images
  v2Images: V2Images;

  // Extra Elements
  flexibleInputs: FlexibleInput[];

  // Background & Visual Direction
  backgroundImages: BackgroundImage[];
  visualDirection: string;

  // Generation state
  isGenerating: boolean;
  progress: number;
  currentQueueId: string | null;
  apiVersion: 'v1' | 'v2' | null;

  // Results
  generatedImages: GeneratedImage[];
  selectedImages: string[];

  // UI state
  showPromptHistory: boolean;
  selectedImageForView: GeneratedImage | null;

  // Technical settings
  technicalSettings: typeof DEFAULT_GENERATION_SETTINGS;
}

type Action =
  | { type: 'SET_MODELS'; payload: string[] }
  | { type: 'SET_ANGLES'; payload: number[] }
  | { type: 'SET_GARMENTS'; payload: string[] }
  | { type: 'SET_SETTINGS'; payload: Record<string, number> }
  | { type: 'SET_V2_IMAGES'; payload: V2Images }
  | { type: 'SET_FLEXIBLE_INPUTS'; payload: FlexibleInput[] }
  | { type: 'SET_BACKGROUND_IMAGES'; payload: BackgroundImage[] }
  | { type: 'SET_VISUAL_DIRECTION'; payload: string }
  | { type: 'SET_TECHNICAL_SETTINGS'; payload: Partial<typeof DEFAULT_GENERATION_SETTINGS> }
  | { type: 'START_GENERATION'; payload: { queueId: string; apiVersion: 'v1' | 'v2' } }
  | { type: 'UPDATE_PROGRESS'; payload: number }
  | { type: 'COMPLETE_GENERATION'; payload: GeneratedImage[] }
  | { type: 'FAIL_GENERATION'; payload: string }
  | { type: 'TOGGLE_IMAGE_SELECTION'; payload: string }
  | { type: 'SET_IMAGE_FOR_VIEW'; payload: GeneratedImage | null }
  | { type: 'TOGGLE_PROMPT_HISTORY' }
  | { type: 'RESET_SELECTIONS' };

function generatorReducer(state: GeneratorState, action: Action): GeneratorState {
  switch (action.type) {
    case 'SET_MODELS':
      return { ...state, selectedModels: action.payload };
    case 'SET_ANGLES':
      return { ...state, selectedAngles: action.payload };
    case 'SET_GARMENTS':
      return { ...state, selectedGarments: action.payload };
    case 'SET_SETTINGS':
      return { ...state, selectedSettings: action.payload };
    case 'SET_V2_IMAGES':
      return { ...state, v2Images: action.payload };
    case 'SET_FLEXIBLE_INPUTS':
      return { ...state, flexibleInputs: action.payload };
    case 'SET_BACKGROUND_IMAGES':
      return { ...state, backgroundImages: action.payload };
    case 'SET_VISUAL_DIRECTION':
      return { ...state, visualDirection: action.payload };
    case 'SET_TECHNICAL_SETTINGS': {
      return {
        ...state,
        technicalSettings: { ...state.technicalSettings, ...action.payload }
      };
    }
    case 'START_GENERATION':
      return { 
        ...state, 
        isGenerating: true, 
        progress: 0, 
        currentQueueId: action.payload.queueId,
        apiVersion: action.payload.apiVersion 
      };
    case 'UPDATE_PROGRESS':
      return { ...state, progress: action.payload };
    case 'COMPLETE_GENERATION':
      return { 
        ...state, 
        isGenerating: false, 
        progress: 100, 
        currentQueueId: null,
        generatedImages: [...state.generatedImages, ...action.payload] 
      };
    case 'FAIL_GENERATION':
      return { 
        ...state, 
        isGenerating: false, 
        progress: 0, 
        currentQueueId: null 
      };
    case 'TOGGLE_IMAGE_SELECTION':
      return {
        ...state,
        selectedImages: state.selectedImages.includes(action.payload)
          ? state.selectedImages.filter(id => id !== action.payload)
          : [...state.selectedImages, action.payload]
      };
    case 'SET_IMAGE_FOR_VIEW':
      return { ...state, selectedImageForView: action.payload };
    case 'TOGGLE_PROMPT_HISTORY':
      return { ...state, showPromptHistory: !state.showPromptHistory };
    case 'RESET_SELECTIONS':
      return {
        ...state,
        selectedModels: [],
        selectedAngles: [],
        selectedGarments: [],
        selectedSettings: {},
        v2Images: {},
      };
    default:
      return state;
  }
}

const initialState: GeneratorState = {
  selectedModels: [],
  selectedAngles: [],
  selectedGarments: [],
  selectedSettings: {},
  v2Images: {},
  flexibleInputs: [],
  backgroundImages: [],
  visualDirection: '',
  isGenerating: false,
  progress: 0,
  currentQueueId: null,
  apiVersion: null,
  generatedImages: [],
  selectedImages: [],
  showPromptHistory: false,
  selectedImageForView: null,
  technicalSettings: DEFAULT_GENERATION_SETTINGS,
};

export function ImageGeneratorContainer() {
  const navigate = useNavigate();
  const { collectionId } = useParams<{ collectionId: string }>();
  const [state, dispatch] = useReducer(generatorReducer, initialState);

  // Fetch collection data
  const { data: collection } = useCollection(collectionId);

  // Fetch organization data if collection exists
  const { data: organization } = useOrganization(collection?.organization_id);
  
  // Prompt state
  const [prompt, setPrompt] = useState('');
  const [activeBlocks, setActiveBlocks] = useState<ActiveBlock[]>([]);
  const [promptHistory, setPromptHistory] = useState<string[]>([]);
  const [uploadedGarments, setUploadedGarments] = useState<any[]>([]);

  // Use Fashion Lab V2 API hook for direct V2 generation
  const {
    generate: generateV2,
    isGenerating: isGeneratingV2,
    progress: progressV2,
    activeQueueIds,
    generatedImages: v2GeneratedImages,
  } = useFashionLabImages({
    collectionId: collectionId!,
    onSuccess: (queueId) => {
      console.log('V2 Generation started:', queueId);
    },
    onComplete: (images) => {
      console.log('V2 Generation complete:', images);
      // Refresh the generated images in the state
      dispatch({ type: 'COMPLETE_GENERATION', payload: [] }); // Will be updated by the query
    }
  });

  // Build prompt from selections
  const buildPrompt = useCallback(() => {
    const parts: string[] = ['Place'];
    
    // Model
    if (state.selectedModels.length > 0) {
      const model = CAMPAIGN_MODELS.find(m => m.id === state.selectedModels[0]);
      if (model) parts.push(model.promptText);
    }
    
    // Garments
    state.selectedGarments.forEach((garmentId, index) => {
      const garment = uploadedGarments.find(g => g.id === garmentId);
      if (garment && !garment.uploading) {
        const prefix = index === 0 ? 'wearing' : index === 1 ? 'with these' : 'and this';
        parts.push(`${prefix} ${garment.promptText}`);
      }
    });
    
    // Background
    if (state.selectedSettings.background) {
      parts.push('on a white studio background'); // Simplified for demo
    }
    
    // Angle
    if (state.selectedAngles.length > 0) {
      const angle = ANGLE_BLOCKS.find(a => a.id === state.selectedAngles[0]);
      if (angle) parts.push(angle.promptText);
    }
    
    return parts.filter(p => p).join(', ');
  }, [state.selectedModels, state.selectedAngles, state.selectedGarments, state.selectedSettings, uploadedGarments]);

  // Update prompt when selections change
  useEffect(() => {
    const newPrompt = buildPrompt();
    setPrompt(newPrompt);
  }, [buildPrompt]);

  // Generate images
  const handleGenerate = useCallback(async () => {
    const targetCollectionId = collectionId || await getOrCreateDemoCollection();
    if (!targetCollectionId) {
      toast.error('Failed to get collection ID');
      return;
    }

    // Save to history
    if (prompt && !promptHistory.includes(prompt)) {
      setPromptHistory(prev => [prompt, ...prev].slice(0, 10));
    }

    // Determine API version
    const useV2 = state.v2Images.face?.base64 && 
                  state.v2Images.image_2?.base64 && 
                  state.v2Images.image_3?.base64 && 
                  state.v2Images.image_4?.base64;

    try {
      if (useV2) {
        // V2 API - Use the hook directly
        await generateV2({
          prompt,
          faceImage: state.v2Images.face!.base64!,
          image2: state.v2Images.image_2!.base64!,
          image3: state.v2Images.image_3!.base64!,
          image4: state.v2Images.image_4!.base64!,
          // Include seed values from technical settings
          seed1: state.technicalSettings.seed,
          seed2: state.technicalSettings.seed,
          seed3: state.technicalSettings.seed,
          seed4: state.technicalSettings.seed,
          numImages: state.technicalSettings.numImages,
          metadata: {
            source: 'image-generator-v2',
            selectedModels: state.selectedModels,
            selectedAngles: state.selectedAngles,
            technicalSettings: state.technicalSettings,
          }
        });

        // The hook handles the rest (polling, completion, etc.)
        dispatch({ type: 'START_GENERATION', payload: { queueId: 'v2-hook-managed', apiVersion: 'v2' } });
        toast.success('V2 Image generation started!');
      } else {
        // V1 API - Generate for each model/angle combination
        const combinations: Array<{ model: Model; angle: AngleBlock }> = [];
        for (const modelId of state.selectedModels) {
          const model = CAMPAIGN_MODELS.find(m => m.id === modelId);
          if (!model) continue;
          
          for (const angleId of state.selectedAngles) {
            const angle = ANGLE_BLOCKS.find(a => a.id === angleId);
            if (!angle) continue;
            combinations.push({ model, angle });
          }
        }

        if (combinations.length === 0) {
          toast.error('Please select at least one model and angle');
          return;
        }

        // Generate all combinations
        for (const { model, angle } of combinations) {
          const params: UnifiedGenerationParams = {
            prompt,
            collectionId: targetCollectionId,
            modelId: model.id,
            loraName: model.lora,
            loraWeight: 1.0,
            angle: angle.name,
            seed: state.technicalSettings.seed || undefined,
            cfg: state.technicalSettings.cfg,
            fluxGuidance: state.technicalSettings.fluxGuidance,
            numImages: state.technicalSettings.numImages,
            aspectRatio: state.technicalSettings.ratio,
            format: state.technicalSettings.imageFormat,
            metadata: {
              modelName: model.name,
              angleName: angle.name,
            }
          };

          const result = await UnifiedImageGenerationService.generateImages(params);
          dispatch({ type: 'START_GENERATION', payload: { queueId: result.queueId, apiVersion: 'v1' } });

          // For V1, we might get immediate results
          if (result.status === 'completed' && result.images) {
            const formattedImages = result.images.map((url, idx) => ({
              id: `${model.id}_${angle.id}_${Date.now()}_${idx}`,
              url,
              modelId: model.id,
              modelName: model.name,
              angleId: angle.id,
              angleName: angle.name,
              prompt,
              seed: state.technicalSettings.seed || undefined,
              timestamp: new Date().toISOString(),
              status: 'generated'
            }));
            dispatch({ type: 'COMPLETE_GENERATION', payload: formattedImages });
          }
        }
        
        toast.success('Generation started!');
      }
    } catch (error) {
      console.error('Generation error:', error);
      dispatch({ type: 'FAIL_GENERATION', payload: error instanceof Error ? error.message : 'Generation failed' });
      toast.error('Failed to generate images');
    }
  }, [collectionId, prompt, promptHistory, state, generateV2]);

  const totalImagesToGenerate = state.selectedModels.length * state.selectedAngles.length * state.technicalSettings.numImages;
  const canGenerateV1 = state.selectedModels.length > 0 && state.selectedAngles.length > 0;
  const canGenerateV2 = state.v2Images.face?.base64 && 
                        state.v2Images.image_2?.base64 && 
                        state.v2Images.image_3?.base64 && 
                        state.v2Images.image_4?.base64;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-40">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (organization?.id && collectionId) {
                    navigate(`/organizations/${organization.id}/collections/${collectionId}`);
                  } else {
                    navigate(-1);
                  }
                }}
                className="gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Collection
              </Button>
              <div className="h-6 w-px bg-border" />
              <h1 className="text-xl font-semibold">FashionLab Image Generator</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-60px)]">
        {/* Left Sidebar - Selection Panel */}
        <ImageSelectionPanel
          selectedModels={state.selectedModels}
          selectedAngles={state.selectedAngles}
          selectedGarments={state.selectedGarments}
          selectedSettings={state.selectedSettings}
          uploadedGarments={uploadedGarments}
          onModelsChange={(models) => dispatch({ type: 'SET_MODELS', payload: models })}
          onAnglesChange={(angles) => dispatch({ type: 'SET_ANGLES', payload: angles })}
          onGarmentsChange={(garments) => dispatch({ type: 'SET_GARMENTS', payload: garments })}
          onSettingsChange={(settings) => dispatch({ type: 'SET_SETTINGS', payload: settings })}
          onGarmentsUpload={setUploadedGarments}
        />

        {/* Main Content - Generated Images */}
        <div className="flex-1 overflow-auto p-4">
          <GeneratedImageGrid
            images={state.generatedImages}
            selectedImages={state.selectedImages}
            onImageSelect={(id) => dispatch({ type: 'TOGGLE_IMAGE_SELECTION', payload: id })}
            onImageView={(image) => dispatch({ type: 'SET_IMAGE_FOR_VIEW', payload: image })}
            models={CAMPAIGN_MODELS}
            angles={ANGLE_BLOCKS}
          />
        </div>

        {/* Right Sidebar - Generation Controls */}
        <div className="w-96 bg-white border-l flex flex-col">
          <div className="p-4 border-b">
            <h3 className="font-medium text-sm">Generation Settings</h3>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* Extra Elements */}
            <FlexibleInputBox
              inputs={state.flexibleInputs}
              onInputsChange={(inputs) => dispatch({ type: 'SET_FLEXIBLE_INPUTS', payload: inputs })}
              maxInputs={10}
            />

            {/* Background & Visual Direction */}
            <BackgroundVisualDirection
              backgroundImages={state.backgroundImages}
              visualDirection={state.visualDirection}
              onBackgroundImagesChange={(images) => dispatch({ type: 'SET_BACKGROUND_IMAGES', payload: images })}
              onVisualDirectionChange={(direction) => dispatch({ type: 'SET_VISUAL_DIRECTION', payload: direction })}
            />

            {/* V2 Image Uploader */}
            <V2ImageUploader
              images={state.v2Images}
              onImagesChange={(images) => dispatch({ type: 'SET_V2_IMAGES', payload: images })}
            />

            {/* Prompt Builder */}
            <PromptBuilder
              prompt={prompt}
              onPromptChange={setPrompt}
              activeBlocks={activeBlocks}
              onActiveBlocksChange={setActiveBlocks}
              onShowHistory={() => dispatch({ type: 'TOGGLE_PROMPT_HISTORY' })}
            />

            {/* Technical Settings */}
            <TechnicalSettings
              settings={state.technicalSettings}
              onSettingsChange={(settings) => dispatch({ type: 'SET_TECHNICAL_SETTINGS', payload: settings })}
              totalImages={totalImagesToGenerate}
            />
          </div>

          {/* Generate Button */}
          <div className="p-4 border-t bg-white">
            <Button 
              onClick={handleGenerate} 
              disabled={state.isGenerating || (!canGenerateV1 && !canGenerateV2)} 
              className="w-full" 
              size="lg"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              {canGenerateV2 ? 'Generate with V2 API' : `Generate ${totalImagesToGenerate} Images`}
            </Button>
            
            {state.isGenerating && (
              <div className="mt-2 space-y-1">
                <Progress value={state.progress} className="h-2" />
                <p className="text-xs text-center text-gray-600">
                  {state.apiVersion === 'v2' ? 'Processing V2 API request...' : 'Generating images...'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Prompt History Dialog */}
      <Dialog open={state.showPromptHistory} onOpenChange={() => dispatch({ type: 'TOGGLE_PROMPT_HISTORY' })}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Prompt History</DialogTitle>
          </DialogHeader>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {promptHistory.length > 0 ? (
              promptHistory.map((historyPrompt, index) => (
                <Card key={index} className="p-3 cursor-pointer hover:bg-gray-50" onClick={() => {
                  setPrompt(historyPrompt);
                  dispatch({ type: 'TOGGLE_PROMPT_HISTORY' });
                }}>
                  <p className="text-sm line-clamp-2">{historyPrompt}</p>
                </Card>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center py-8">No saved prompts yet</p>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Detail Modal */}
      {state.selectedImageForView && (
        <ImageDetailModal
          image={state.selectedImageForView}
          onClose={() => dispatch({ type: 'SET_IMAGE_FOR_VIEW', payload: null })}
        />
      )}
    </div>
  );
}